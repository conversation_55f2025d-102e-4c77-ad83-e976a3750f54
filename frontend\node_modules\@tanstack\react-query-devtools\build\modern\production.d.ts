import { R as ReactQueryDevtools$1 } from './ReactQueryDevtools-DO8QvfQP.js';
import { R as ReactQueryDevtoolsPanel$1 } from './ReactQueryDevtoolsPanel-BAUD7o3r.js';
import 'react';
import '@tanstack/query-devtools';
import '@tanstack/react-query';

declare const ReactQueryDevtools: typeof ReactQueryDevtools$1;
declare const ReactQueryDevtoolsPanel: typeof ReactQueryDevtoolsPanel$1;

export { ReactQueryDevtools, ReactQueryDevtoolsPanel };
