import React, { useState, useMemo } from 'react';
import { 
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from '@heroicons/react/24/outline';

interface ComparisonViewProps {
  originalContent: string;
  modifiedContent?: string;
  showOriginal: boolean;
  showModified: boolean;
  className?: string;
}

interface DiffSegment {
  type: 'equal' | 'delete' | 'insert' | 'replace';
  originalText: string;
  modifiedText: string;
  originalStart: number;
  originalEnd: number;
  modifiedStart: number;
  modifiedEnd: number;
}

const ComparisonView: React.FC<ComparisonViewProps> = ({
  originalContent,
  modifiedContent,
  showOriginal,
  showModified,
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightDifferences, setHighlightDifferences] = useState(true);
  const [syncScroll, setSyncScroll] = useState(true);
  const [fontSize, setFontSize] = useState(16);

  // 简单的文本差异检测（实际项目中可以使用更复杂的diff算法）
  const diffSegments = useMemo((): DiffSegment[] => {
    if (!modifiedContent) {
      return [{
        type: 'equal',
        originalText: originalContent,
        modifiedText: '',
        originalStart: 0,
        originalEnd: originalContent.length,
        modifiedStart: 0,
        modifiedEnd: 0,
      }];
    }

    // 简单的段落级别差异检测
    const originalParagraphs = originalContent.split('\n\n');
    const modifiedParagraphs = modifiedContent.split('\n\n');
    const segments: DiffSegment[] = [];

    const maxLength = Math.max(originalParagraphs.length, modifiedParagraphs.length);
    
    for (let i = 0; i < maxLength; i++) {
      const originalPara = originalParagraphs[i] || '';
      const modifiedPara = modifiedParagraphs[i] || '';

      if (originalPara === modifiedPara) {
        segments.push({
          type: 'equal',
          originalText: originalPara,
          modifiedText: modifiedPara,
          originalStart: i,
          originalEnd: i + 1,
          modifiedStart: i,
          modifiedEnd: i + 1,
        });
      } else if (!originalPara) {
        segments.push({
          type: 'insert',
          originalText: '',
          modifiedText: modifiedPara,
          originalStart: i,
          originalEnd: i,
          modifiedStart: i,
          modifiedEnd: i + 1,
        });
      } else if (!modifiedPara) {
        segments.push({
          type: 'delete',
          originalText: originalPara,
          modifiedText: '',
          originalStart: i,
          originalEnd: i + 1,
          modifiedStart: i,
          modifiedEnd: i,
        });
      } else {
        segments.push({
          type: 'replace',
          originalText: originalPara,
          modifiedText: modifiedPara,
          originalStart: i,
          originalEnd: i + 1,
          modifiedStart: i,
          modifiedEnd: i + 1,
        });
      }
    }

    return segments;
  }, [originalContent, modifiedContent]);

  // 高亮搜索词
  const highlightSearchTerm = (text: string) => {
    if (!searchTerm || !text) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
  };

  // 渲染差异段落
  const renderDiffSegment = (segment: DiffSegment, isOriginal: boolean) => {
    const text = isOriginal ? segment.originalText : segment.modifiedText;
    if (!text) return null;

    let className = 'p-4 border-l-4 ';
    
    if (highlightDifferences) {
      switch (segment.type) {
        case 'equal':
          className += 'border-gray-300 bg-white';
          break;
        case 'delete':
          className += isOriginal ? 'border-red-400 bg-red-50' : 'border-gray-300 bg-gray-50';
          break;
        case 'insert':
          className += !isOriginal ? 'border-green-400 bg-green-50' : 'border-gray-300 bg-gray-50';
          break;
        case 'replace':
          className += isOriginal ? 'border-yellow-400 bg-yellow-50' : 'border-blue-400 bg-blue-50';
          break;
      }
    } else {
      className += 'border-gray-300 bg-white';
    }

    return (
      <div key={`${segment.originalStart}-${segment.modifiedStart}`} className={className}>
        <div 
          className="whitespace-pre-wrap leading-relaxed"
          style={{ fontSize: `${fontSize}px` }}
          dangerouslySetInnerHTML={{ __html: highlightSearchTerm(text) }}
        />
      </div>
    );
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (!syncScroll) return;
    
    const target = e.target as HTMLDivElement;
    const otherPanel = target.parentElement?.querySelector('.scroll-sync:not(:hover)') as HTMLDivElement;
    
    if (otherPanel) {
      otherPanel.scrollTop = target.scrollTop;
    }
  };

  // 计算显示模式
  const showBoth = showOriginal && showModified;
  const showOnlyOriginal = showOriginal && !showModified;
  const showOnlyModified = !showOriginal && showModified;

  return (
    <div className={`h-full flex flex-col bg-gray-50 ${className}`}>
      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 搜索框 */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索内容..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* 字体大小控制 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">字体:</span>
              <button
                onClick={() => setFontSize(Math.max(12, fontSize - 2))}
                className="px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
              >
                A-
              </button>
              <span className="text-sm text-gray-600">{fontSize}px</span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                className="px-2 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
              >
                A+
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* 差异高亮开关 */}
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={highlightDifferences}
                onChange={(e) => setHighlightDifferences(e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-gray-600">高亮差异</span>
            </label>

            {/* 同步滚动开关 */}
            {showBoth && (
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={syncScroll}
                  onChange={(e) => setSyncScroll(e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="ml-2 text-sm text-gray-600">同步滚动</span>
              </label>
            )}

            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md">
              <AdjustmentsHorizontalIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 原文面板 */}
        {showOriginal && (
          <div className={`${showBoth ? 'w-1/2' : 'w-full'} flex flex-col border-r border-gray-200`}>
            <div className="bg-blue-50 border-b border-blue-200 px-4 py-2">
              <h3 className="text-sm font-medium text-blue-800">原文</h3>
            </div>
            <div 
              className="flex-1 overflow-auto scroll-sync"
              onScroll={handleScroll}
            >
              <div className="space-y-1">
                {diffSegments.map((segment, index) => 
                  renderDiffSegment(segment, true)
                )}
              </div>
            </div>
          </div>
        )}

        {/* 修改版面板 */}
        {showModified && (
          <div className={`${showBoth ? 'w-1/2' : 'w-full'} flex flex-col`}>
            <div className="bg-green-50 border-b border-green-200 px-4 py-2">
              <h3 className="text-sm font-medium text-green-800">
                修改版 {!modifiedContent && <span className="text-gray-500">(暂无修改版本)</span>}
              </h3>
            </div>
            <div 
              className="flex-1 overflow-auto scroll-sync"
              onScroll={handleScroll}
            >
              {modifiedContent ? (
                <div className="space-y-1">
                  {diffSegments.map((segment, index) => 
                    renderDiffSegment(segment, false)
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <p className="text-lg mb-2">暂无修改版本</p>
                    <p className="text-sm">请先对章节进行AI修改</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 无内容显示时的占位 */}
        {!showOriginal && !showModified && (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <p className="text-lg mb-2">请选择要显示的内容</p>
              <p className="text-sm">使用顶部的切换按钮选择原文或修改版</p>
            </div>
          </div>
        )}
      </div>

      {/* 底部统计信息 */}
      <div className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-6">
            <span>原文字数: {originalContent.length.toLocaleString()}</span>
            {modifiedContent && (
              <span>修改版字数: {modifiedContent.length.toLocaleString()}</span>
            )}
            {modifiedContent && (
              <span>
                变化: {modifiedContent.length > originalContent.length ? '+' : ''}
                {(modifiedContent.length - originalContent.length).toLocaleString()}
              </span>
            )}
          </div>
          
          {highlightDifferences && modifiedContent && (
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <div className="w-3 h-3 bg-red-200 border border-red-400 rounded mr-1"></div>
                删除
              </span>
              <span className="flex items-center">
                <div className="w-3 h-3 bg-green-200 border border-green-400 rounded mr-1"></div>
                新增
              </span>
              <span className="flex items-center">
                <div className="w-3 h-3 bg-yellow-200 border border-yellow-400 rounded mr-1"></div>
                修改
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComparisonView;
