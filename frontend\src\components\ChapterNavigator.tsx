import React, { useState, useMemo } from 'react';
import { Link, useParams } from 'react-router-dom';
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { Chapter, ChapterStatus } from '../types';

interface ChapterNavigatorProps {
  chapters: Chapter[];
  currentChapterId?: string;
  isLoading?: boolean;
  className?: string;
}

interface FilterOptions {
  status: ChapterStatus | 'ALL';
  searchTerm: string;
}

const ChapterNavigator: React.FC<ChapterNavigatorProps> = ({
  chapters,
  currentChapterId,
  isLoading = false,
  className = '',
}) => {
  const { novelId } = useParams<{ novelId: string }>();
  const [filters, setFilters] = useState<FilterOptions>({
    status: 'ALL',
    searchTerm: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set(['recent']));

  // 过滤章节
  const filteredChapters = useMemo(() => {
    return chapters.filter(chapter => {
      // 状态过滤
      if (filters.status !== 'ALL' && chapter.status !== filters.status) {
        return false;
      }
      
      // 搜索过滤
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        return (
          chapter.title.toLowerCase().includes(searchLower) ||
          chapter.number.toString().includes(searchLower)
        );
      }
      
      return true;
    });
  }, [chapters, filters]);

  // 按状态分组章节
  const groupedChapters = useMemo(() => {
    const groups: Record<string, Chapter[]> = {
      modified: [],
      analyzing: [],
      original: [],
      error: [],
    };

    filteredChapters.forEach(chapter => {
      switch (chapter.status) {
        case 'MODIFIED':
          groups.modified.push(chapter);
          break;
        case 'MODIFYING':
        case 'ANALYZING':
          groups.analyzing.push(chapter);
          break;
        case 'ERROR':
          groups.error.push(chapter);
          break;
        default:
          groups.original.push(chapter);
      }
    });

    return groups;
  }, [filteredChapters]);

  const toggleGroup = (groupId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId);
    } else {
      newExpanded.add(groupId);
    }
    setExpandedGroups(newExpanded);
  };

  const getStatusIcon = (status: ChapterStatus) => {
    switch (status) {
      case 'MODIFIED':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'MODIFYING':
      case 'ANALYZING':
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
      case 'ERROR':
        return <ExclamationCircleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <DocumentTextIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: ChapterStatus) => {
    switch (status) {
      case 'MODIFIED':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'MODIFYING':
      case 'ANALYZING':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'ERROR':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const statusOptions = [
    { value: 'ALL', label: '全部状态' },
    { value: 'ORIGINAL', label: '原始' },
    { value: 'ANALYZED', label: '已分析' },
    { value: 'MODIFIED', label: '已修改' },
    { value: 'MODIFYING', label: '修改中' },
    { value: 'ERROR', label: '错误' },
  ];

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
        <div className="p-4 border-b border-gray-200">
          <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="p-4 space-y-3">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* 标题和搜索 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">章节导航</h3>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-1 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
          >
            <FunnelIcon className="h-5 w-5" />
          </button>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="搜索章节..."
            value={filters.searchTerm}
            onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        {/* 过滤器 */}
        {showFilters && (
          <div className="mt-3 p-3 bg-gray-50 rounded-md">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">状态过滤</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any }))}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* 章节列表 */}
      <div className="max-h-96 overflow-y-auto">
        {filteredChapters.length === 0 ? (
          <div className="p-8 text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">
              {filters.searchTerm || filters.status !== 'ALL' ? '没有找到匹配的章节' : '暂无章节'}
            </p>
          </div>
        ) : (
          <div className="p-2">
            {/* 按状态分组显示 */}
            {Object.entries(groupedChapters).map(([groupKey, groupChapters]) => {
              if (groupChapters.length === 0) return null;

              const groupNames = {
                modified: '已修改',
                analyzing: '处理中',
                original: '原始章节',
                error: '错误章节',
              };

              const groupName = groupNames[groupKey as keyof typeof groupNames];
              const isExpanded = expandedGroups.has(groupKey);

              return (
                <div key={groupKey} className="mb-4">
                  <button
                    onClick={() => toggleGroup(groupKey)}
                    className="flex items-center w-full px-2 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
                  >
                    {isExpanded ? (
                      <ChevronDownIcon className="h-4 w-4 mr-1" />
                    ) : (
                      <ChevronRightIcon className="h-4 w-4 mr-1" />
                    )}
                    {groupName} ({groupChapters.length})
                  </button>

                  {isExpanded && (
                    <div className="ml-4 mt-1 space-y-1">
                      {groupChapters.map((chapter) => (
                        <Link
                          key={chapter.id}
                          to={`/chapters/${chapter.id}`}
                          className={`block px-3 py-2 rounded-md text-sm transition-colors ${
                            currentChapterId === chapter.id
                              ? 'bg-primary-100 text-primary-700 border-l-2 border-primary-500'
                              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center min-w-0 flex-1">
                              {getStatusIcon(chapter.status)}
                              <span className="ml-2 font-medium text-xs text-gray-500">
                                第{chapter.number}章
                              </span>
                              <span className="ml-2 truncate">
                                {chapter.title}
                              </span>
                            </div>
                            <div className="ml-2 text-xs text-gray-400">
                              {Math.round(chapter.wordCount / 1000)}k字
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">总章节:</span>
            <span className="ml-1 font-medium text-gray-900">{chapters.length}</span>
          </div>
          <div>
            <span className="text-gray-500">已修改:</span>
            <span className="ml-1 font-medium text-green-600">
              {chapters.filter(c => c.status === 'MODIFIED').length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChapterNavigator;
