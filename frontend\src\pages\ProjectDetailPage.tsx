import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  PlusIcon,
  BookOpenIcon,
  CogIcon,
  DocumentArrowUpIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline';
import { useProject, useNovels, useImportNovel } from '../hooks/useApi';
import { Novel, NovelImportInput } from '../types';
import ImportNovelModal from '../components/ImportNovelModal';

const ProjectDetailPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [showImportModal, setShowImportModal] = useState(false);
  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);

  const { data: project, isLoading: projectLoading } = useProject(projectId!);
  const { data: novels, isLoading: novelsLoading } = useNovels(projectId);
  const importNovelMutation = useImportNovel();

  const handleImportNovel = async (data: NovelImportInput) => {
    try {
      await importNovelMutation.mutateAsync(data);
      setShowImportModal(false);
    } catch (error) {
      console.error('导入小说失败:', error);
    }
  };

  if (projectLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96"></div>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">项目不存在</h1>
        <Link to="/projects" className="btn-primary">
          返回项目列表
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 项目信息头部 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{project.name}</h1>
            <p className="text-gray-600 mb-4">
              {project.description || '暂无项目描述'}
            </p>
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span>创建时间: {new Date(project.createdAt).toLocaleDateString()}</span>
              <span>小说数量: {novels?.length || 0}</span>
              <span>最后更新: {new Date(project.updatedAt).toLocaleDateString()}</span>
            </div>
          </div>

          <div className="flex space-x-3">
            <button className="btn-secondary flex items-center">
              <CogIcon className="h-4 w-4 mr-2" />
              项目设置
            </button>
            <button
              onClick={() => setShowImportModal(true)}
              className="btn-primary flex items-center"
            >
              <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
              导入小说
            </button>
          </div>
        </div>
      </div>

      {/* 小说列表 */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">项目小说</h2>
            <button
              onClick={() => setShowImportModal(true)}
              className="btn-secondary flex items-center text-sm"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              添加小说
            </button>
          </div>
        </div>

        <div className="p-6">
          {novelsLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse border border-gray-200 rounded-lg p-4">
                  <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              ))}
            </div>
          ) : novels && novels.length > 0 ? (
            <div className="space-y-4">
              {novels.map((novel) => (
                <div key={novel.id} className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <BookOpenIcon className="h-5 w-5 text-primary-500 mr-2" />
                        <h3 className="font-semibold text-gray-900">{novel.title}</h3>
                        <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          novel.status === 'ANALYZED' ? 'bg-green-100 text-green-800' :
                          novel.status === 'ANALYZING' ? 'bg-yellow-100 text-yellow-800' :
                          novel.status === 'IMPORTED' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {novel.status === 'ANALYZED' ? '已分析' :
                           novel.status === 'ANALYZING' ? '分析中' :
                           novel.status === 'IMPORTED' ? '已导入' : novel.status}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                        <div>作者: {novel.author || '未知'}</div>
                        <div>章节: {novel.totalChapters}</div>
                        <div>字数: {novel.totalWords.toLocaleString()}</div>
                        <div>导入时间: {new Date(novel.createdAt).toLocaleDateString()}</div>
                      </div>

                      {novel.summary && (
                        <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                          {novel.summary}
                        </p>
                      )}
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        to={`/novels/${novel.id}`}
                        className="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
                        title="查看详情"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </Link>

                      <button
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        title="编辑"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>

                      <button
                        onClick={() => setSelectedNovel(
                          selectedNovel?.id === novel.id ? null : novel
                        )}
                        className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                      >
                        <EllipsisVerticalIcon className="h-4 w-4" />
                      </button>

                      {selectedNovel?.id === novel.id && (
                        <div className="absolute right-0 mt-8 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                          <div className="py-1">
                            <button className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                              重新分析
                            </button>
                            <button className="flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                              <TrashIcon className="h-4 w-4 mr-3" />
                              删除小说
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">还没有小说</h3>
              <p className="text-gray-500 mb-6">
                导入您的第一部小说开始使用AI重塑功能
              </p>
              <button
                onClick={() => setShowImportModal(true)}
                className="btn-primary flex items-center mx-auto"
              >
                <DocumentArrowUpIcon className="h-4 w-4 mr-2" />
                导入小说
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 导入小说模态框 */}
      {showImportModal && (
        <ImportNovelModal
          isOpen={showImportModal}
          onClose={() => setShowImportModal(false)}
          onSubmit={handleImportNovel}
          projectId={projectId!}
          isLoading={importNovelMutation.isPending}
        />
      )}

      {/* 点击外部关闭菜单 */}
      {selectedNovel && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setSelectedNovel(null)}
        />
      )}
    </div>
  );
};

export default ProjectDetailPage;
