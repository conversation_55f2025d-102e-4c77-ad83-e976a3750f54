{"name": "@tanstack/react-query-devtools", "version": "5.90.1", "description": "Developer tools to interact with and visualize the TanStack/react-query cache", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/react-query-devtools"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "types": "build/legacy/index.d.ts", "main": "build/legacy/index.cjs", "module": "build/legacy/index.js", "exports": {".": {"@tanstack/custom-condition": "./src/index.ts", "import": {"types": "./build/modern/index.d.ts", "default": "./build/modern/index.js"}, "require": {"types": "./build/modern/index.d.cts", "default": "./build/modern/index.cjs"}}, "./production": {"import": {"types": "./build/modern/production.d.ts", "default": "./build/modern/production.js"}, "require": {"types": "./build/modern/production.d.cts", "default": "./build/modern/production.cjs"}}, "./build/modern/production.js": {"import": {"types": "./build/modern/production.d.ts", "default": "./build/modern/production.js"}, "require": {"types": "./build/modern/production.d.cts", "default": "./build/modern/production.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["build", "src", "!src/__tests__"], "dependencies": {"@tanstack/query-devtools": "5.90.1"}, "devDependencies": {"@testing-library/react": "^16.1.0", "@types/react": "^19.0.1", "@vitejs/plugin-react": "^4.3.4", "npm-run-all2": "^5.0.0", "react": "^19.0.0", "@tanstack/react-query": "5.90.1"}, "peerDependencies": {"react": "^18 || ^19", "@tanstack/react-query": "^5.90.1"}, "scripts": {"clean": "premove ./build ./coverage ./dist-ts", "compile": "tsc --build", "test:eslint": "eslint --concurrency=auto ./src", "test:types": "npm-run-all --serial test:types:*", "test:types:ts50": "node ../../node_modules/typescript50/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts51": "node ../../node_modules/typescript51/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts52": "node ../../node_modules/typescript52/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts53": "node ../../node_modules/typescript53/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts54": "node ../../node_modules/typescript54/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts55": "node ../../node_modules/typescript55/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts56": "node ../../node_modules/typescript56/lib/tsc.js --build tsconfig.legacy.json", "test:types:ts57": "node ../../node_modules/typescript57/lib/tsc.js --build tsconfig.legacy.json", "test:types:tscurrent": "tsc --build", "test:lib": "vitest", "test:lib:dev": "pnpm run test:lib --watch", "test:build": "publint --strict && attw --pack", "build": "tsup --tsconfig tsconfig.prod.json", "build:dev": "tsup --watch"}}