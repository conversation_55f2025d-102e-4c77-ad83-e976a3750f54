import { D as Devtools } from './ReactQueryDevtools-DO8QvfQP.js';
import { D as DevtoolsPanel, a as DevtoolsPanelOptions$1 } from './ReactQueryDevtoolsPanel-BAUD7o3r.js';
import 'react';
import '@tanstack/query-devtools';
import '@tanstack/react-query';

declare const ReactQueryDevtools: (typeof Devtools)['ReactQueryDevtools'];
declare const ReactQueryDevtoolsPanel: (typeof DevtoolsPanel)['ReactQueryDevtoolsPanel'];
type DevtoolsPanelOptions = DevtoolsPanelOptions$1;

export { type DevtoolsPanelOptions, ReactQueryDevtools, ReactQueryDevtoolsPanel };
