import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  BookOpenIcon,
  UserIcon,
  CalendarIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CogIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useNovel, useChapters, useCharacters, usePlotLines, useAnalyzeNovel } from '../hooks/useApi';
import { Chapter } from '../types';

const NovelDetailPage: React.FC = () => {
  const { novelId } = useParams<{ novelId: string }>();
  const [activeTab, setActiveTab] = useState<'chapters' | 'characters' | 'plots'>('chapters');

  const { data: novel, isLoading: novelLoading } = useNovel(novelId!);
  const { data: chaptersData, isLoading: chaptersLoading } = useChapters(novelId!, 1, 50);
  const { data: characters, isLoading: charactersLoading } = useCharacters(novelId!);
  const { data: plotLines, isLoading: plotLinesLoading } = usePlotLines(novelId!);
  const analyzeNovelMutation = useAnalyzeNovel();

  const chapters = chaptersData?.data || [];

  const handleAnalyze = async () => {
    if (novelId) {
      try {
        await analyzeNovelMutation.mutateAsync(novelId);
      } catch (error) {
        console.error('分析小说失败:', error);
      }
    }
  };

  if (novelLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-96"></div>
        </div>
      </div>
    );
  }

  if (!novel) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">小说不存在</h1>
        <Link to="/projects" className="btn-primary">
          返回项目列表
        </Link>
      </div>
    );
  }

  const tabs = [
    { id: 'chapters', name: '章节列表', count: chapters.length },
    { id: 'characters', name: '角色分析', count: characters?.length || 0 },
    { id: 'plots', name: '情节线', count: plotLines?.length || 0 },
  ];

  return (
    <div className="space-y-6">
      {/* 小说信息头部 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center mb-3">
              <BookOpenIcon className="h-8 w-8 text-primary-500 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{novel.title}</h1>
                <div className="flex items-center mt-1 text-sm text-gray-500">
                  <UserIcon className="h-4 w-4 mr-1" />
                  <span className="mr-4">{novel.author || '未知作者'}</span>
                  <CalendarIcon className="h-4 w-4 mr-1" />
                  <span>导入于 {new Date(novel.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {novel.summary && (
              <p className="text-gray-600 mb-4 leading-relaxed">
                {novel.summary}
              </p>
            )}

            {/* 统计信息 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <div>
                    <p className="text-sm text-blue-600 font-medium">总章节</p>
                    <p className="text-lg font-semibold text-blue-900">{novel.totalChapters}</p>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-3">
                <div className="flex items-center">
                  <ChartBarIcon className="h-5 w-5 text-green-500 mr-2" />
                  <div>
                    <p className="text-sm text-green-600 font-medium">总字数</p>
                    <p className="text-lg font-semibold text-green-900">{novel.totalWords.toLocaleString()}</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-3">
                <div className="flex items-center">
                  <UserIcon className="h-5 w-5 text-purple-500 mr-2" />
                  <div>
                    <p className="text-sm text-purple-600 font-medium">角色数</p>
                    <p className="text-lg font-semibold text-purple-900">{characters?.length || 0}</p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 rounded-lg p-3">
                <div className="flex items-center">
                  <div className={`h-3 w-3 rounded-full mr-2 ${
                    novel.status === 'ANALYZED' ? 'bg-green-500' :
                    novel.status === 'ANALYZING' ? 'bg-yellow-500' :
                    novel.status === 'IMPORTED' ? 'bg-blue-500' :
                    'bg-gray-500'
                  }`} />
                  <div>
                    <p className="text-sm text-gray-600 font-medium">状态</p>
                    <p className="text-sm font-semibold text-gray-900">
                      {novel.status === 'ANALYZED' ? '已分析' :
                       novel.status === 'ANALYZING' ? '分析中' :
                       novel.status === 'IMPORTED' ? '已导入' : novel.status}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-3 ml-6">
            <button className="btn-secondary flex items-center">
              <CogIcon className="h-4 w-4 mr-2" />
              设置
            </button>

            {novel.status === 'IMPORTED' && (
              <button
                onClick={handleAnalyze}
                disabled={analyzeNovelMutation.isPending}
                className="btn-primary flex items-center"
              >
                {analyzeNovelMutation.isPending ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    分析中...
                  </>
                ) : (
                  <>
                    <PlayIcon className="h-4 w-4 mr-2" />
                    开始分析
                  </>
                )}
              </button>
            )}

            {novel.status === 'ANALYZED' && (
              <button
                onClick={handleAnalyze}
                disabled={analyzeNovelMutation.isPending}
                className="btn-secondary flex items-center"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                重新分析
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
                {tab.count > 0 && (
                  <span className={`ml-2 py-0.5 px-2 rounded-full text-xs ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-600'
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="p-6">
          {activeTab === 'chapters' && (
            <ChaptersTab
              chapters={chapters}
              isLoading={chaptersLoading}
              novelId={novelId!}
            />
          )}

          {activeTab === 'characters' && (
            <CharactersTab
              characters={characters || []}
              isLoading={charactersLoading}
            />
          )}

          {activeTab === 'plots' && (
            <PlotsTab
              plotLines={plotLines || []}
              isLoading={plotLinesLoading}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// 章节标签页组件
const ChaptersTab: React.FC<{
  chapters: Chapter[];
  isLoading: boolean;
  novelId: string;
}> = ({ chapters, isLoading, novelId }) => {
  if (isLoading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="animate-pulse border border-gray-200 rounded-lg p-4">
            <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    );
  }

  if (chapters.length === 0) {
    return (
      <div className="text-center py-8">
        <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">暂无章节数据</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {chapters.map((chapter) => (
        <div key={chapter.id} className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <span className="text-sm font-medium text-gray-500 mr-3">
                  第 {chapter.number} 章
                </span>
                <h3 className="font-medium text-gray-900">{chapter.title}</h3>
                <span className={`ml-3 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  chapter.status === 'MODIFIED' ? 'bg-green-100 text-green-800' :
                  chapter.status === 'MODIFYING' ? 'bg-yellow-100 text-yellow-800' :
                  chapter.status === 'ANALYZED' ? 'bg-blue-100 text-blue-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {chapter.status === 'MODIFIED' ? '已修改' :
                   chapter.status === 'MODIFYING' ? '修改中' :
                   chapter.status === 'ANALYZED' ? '已分析' :
                   chapter.status === 'ORIGINAL' ? '原始' : chapter.status}
                </span>
              </div>

              <div className="flex items-center text-sm text-gray-500 space-x-4">
                <span>字数: {chapter.wordCount.toLocaleString()}</span>
                {chapter.modifiedAt && (
                  <span>最后修改: {new Date(chapter.modifiedAt).toLocaleDateString()}</span>
                )}
              </div>
            </div>

            <Link
              to={`/chapters/${chapter.id}`}
              className="ml-4 p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
              title="查看章节"
            >
              <EyeIcon className="h-5 w-5" />
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
};

// 角色标签页组件
const CharactersTab: React.FC<{
  characters: any[];
  isLoading: boolean;
}> = ({ characters, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="animate-pulse border border-gray-200 rounded-lg p-4">
            <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          </div>
        ))}
      </div>
    );
  }

  if (characters.length === 0) {
    return (
      <div className="text-center py-8">
        <UserIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">暂无角色数据</p>
        <p className="text-sm text-gray-400 mt-1">请先分析小说以提取角色信息</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {characters.map((character) => (
        <div key={character.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <h3 className="font-medium text-gray-900">{character.name}</h3>
            <span className="text-xs bg-primary-100 text-primary-800 px-2 py-1 rounded-full">
              重要度: {character.importanceScore}
            </span>
          </div>

          <p className="text-sm text-gray-600 mb-2 line-clamp-2">
            {character.description}
          </p>

          <div className="text-xs text-gray-500">
            首次出现: 第 {character.firstAppearance} 章
          </div>
        </div>
      ))}
    </div>
  );
};

// 情节线标签页组件
const PlotsTab: React.FC<{
  plotLines: any[];
  isLoading: boolean;
}> = ({ plotLines, isLoading }) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse border border-gray-200 rounded-lg p-4">
            <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  if (plotLines.length === 0) {
    return (
      <div className="text-center py-8">
        <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">暂无情节线数据</p>
        <p className="text-sm text-gray-400 mt-1">请先分析小说以提取情节信息</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {plotLines.map((plot) => (
        <div key={plot.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center">
              <h3 className="font-medium text-gray-900 mr-3">{plot.name}</h3>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                plot.type === 'MAIN' ? 'bg-red-100 text-red-800' :
                plot.type === 'ROMANCE' ? 'bg-pink-100 text-pink-800' :
                plot.type === 'SIDE' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {plot.type === 'MAIN' ? '主线' :
                 plot.type === 'ROMANCE' ? '感情线' :
                 plot.type === 'SIDE' ? '支线' : '背景'}
              </span>
            </div>

            <span className={`text-xs px-2 py-1 rounded-full ${
              plot.status === 'RESOLVED' ? 'bg-green-100 text-green-800' :
              plot.status === 'ONGOING' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {plot.status === 'RESOLVED' ? '已完结' :
               plot.status === 'ONGOING' ? '进行中' : '已放弃'}
            </span>
          </div>

          <p className="text-sm text-gray-600 mb-2">
            {plot.description}
          </p>

          <div className="text-xs text-gray-500">
            涉及章节: {plot.chapters?.join(', ') || '无'}
          </div>
        </div>
      ))}
    </div>
  );
};

export default NovelDetailPage;
