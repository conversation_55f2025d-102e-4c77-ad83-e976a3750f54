import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  EyeIcon,
  EyeSlashIcon,
  DocumentDuplicateIcon,
  CogIcon,
  PlayIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useChapter, useChapters, useModifyChapter } from '../hooks/useApi';
import { useComparisonState } from '../store';
import ChapterNavigator from '../components/ChapterNavigator';
import ComparisonView from '../components/ComparisonView';

const ChapterViewPage: React.FC = () => {
  const { chapterId } = useParams<{ chapterId: string }>();
  const [showNavigator, setShowNavigator] = useState(true);
  const [showModifyModal, setShowModifyModal] = useState(false);

  const { data: chapter, isLoading: chapterLoading } = useChapter(chapterId!);
  const { data: chaptersData } = useChapters(chapter?.novelId || '', 1, 1000);
  const modifyChapterMutation = useModifyChapter();

  const {
    comparisonMode,
    showOriginal,
    showModified,
    setComparisonMode,
    setShowOriginal,
    setShowModified,
  } = useComparisonState();

  const chapters = chaptersData?.data || [];
  const currentIndex = chapters.findIndex(c => c.id === chapterId);
  const prevChapter = currentIndex > 0 ? chapters[currentIndex - 1] : null;
  const nextChapter = currentIndex < chapters.length - 1 ? chapters[currentIndex + 1] : null;

  const handleModifyChapter = async () => {
    if (chapter) {
      try {
        // TODO: 实现修改规则选择
        await modifyChapterMutation.mutateAsync({
          id: chapter.id,
          rules: [], // 临时空规则
        });
      } catch (error) {
        console.error('修改章节失败:', error);
      }
    }
  };

  if (chapterLoading) {
    return (
      <div className="flex h-screen">
        <div className="flex-1 p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-64"></div>
            <div className="h-4 bg-gray-200 rounded w-96"></div>
            <div className="space-y-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!chapter) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">章节不存在</h1>
        <Link to="/projects" className="btn-primary">
          返回项目列表
        </Link>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 侧边栏导航 */}
      {showNavigator && (
        <div className="w-80 border-r border-gray-200 bg-white">
          <ChapterNavigator
            chapters={chapters}
            currentChapterId={chapterId}
            className="h-full border-0 rounded-none"
          />
        </div>
      )}

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            {/* 左侧：章节信息和导航 */}
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowNavigator(!showNavigator)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                title={showNavigator ? '隐藏导航' : '显示导航'}
              >
                {showNavigator ? <EyeSlashIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
              </button>

              <div className="flex items-center space-x-2">
                {prevChapter && (
                  <Link
                    to={`/chapters/${prevChapter.id}`}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                    title="上一章"
                  >
                    <ArrowLeftIcon className="h-5 w-5" />
                  </Link>
                )}

                <div className="px-3 py-1 bg-gray-100 rounded-md">
                  <span className="text-sm font-medium text-gray-700">
                    第 {chapter.number} 章
                  </span>
                </div>

                {nextChapter && (
                  <Link
                    to={`/chapters/${nextChapter.id}`}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                    title="下一章"
                  >
                    <ArrowRightIcon className="h-5 w-5" />
                  </Link>
                )}
              </div>

              <div>
                <h1 className="text-lg font-semibold text-gray-900">{chapter.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>字数: {chapter.wordCount.toLocaleString()}</span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    chapter.status === 'MODIFIED' ? 'bg-green-100 text-green-800' :
                    chapter.status === 'MODIFYING' ? 'bg-yellow-100 text-yellow-800' :
                    chapter.status === 'ANALYZED' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {chapter.status === 'MODIFIED' ? '已修改' :
                     chapter.status === 'MODIFYING' ? '修改中' :
                     chapter.status === 'ANALYZED' ? '已分析' :
                     chapter.status === 'ORIGINAL' ? '原始' : chapter.status}
                  </span>
                </div>
              </div>
            </div>

            {/* 右侧：操作按钮 */}
            <div className="flex items-center space-x-3">
              {/* 视图控制 */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setComparisonMode(!comparisonMode)}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    comparisonMode
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <DocumentDuplicateIcon className="h-4 w-4 mr-1 inline" />
                  对比模式
                </button>
              </div>

              {comparisonMode && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowOriginal(!showOriginal)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${
                      showOriginal
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    原文
                  </button>
                  <button
                    onClick={() => setShowModified(!showModified)}
                    className={`px-3 py-1 text-sm rounded-md transition-colors ${
                      showModified
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    修改版
                  </button>
                </div>
              )}

              <div className="h-6 border-l border-gray-300"></div>

              <button className="btn-secondary flex items-center text-sm">
                <CogIcon className="h-4 w-4 mr-1" />
                设置
              </button>

              {chapter.status === 'ORIGINAL' || chapter.status === 'ANALYZED' ? (
                <button
                  onClick={handleModifyChapter}
                  disabled={modifyChapterMutation.isPending}
                  className="btn-primary flex items-center text-sm"
                >
                  {modifyChapterMutation.isPending ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" />
                      修改中...
                    </>
                  ) : (
                    <>
                      <PlayIcon className="h-4 w-4 mr-1" />
                      开始修改
                    </>
                  )}
                </button>
              ) : chapter.status === 'MODIFIED' ? (
                <button
                  onClick={handleModifyChapter}
                  disabled={modifyChapterMutation.isPending}
                  className="btn-secondary flex items-center text-sm"
                >
                  <ArrowPathIcon className="h-4 w-4 mr-1" />
                  重新修改
                </button>
              ) : null}
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-hidden">
          {comparisonMode ? (
            <ComparisonView
              originalContent={chapter.originalContent}
              modifiedContent={chapter.modifiedContent}
              showOriginal={showOriginal}
              showModified={showModified}
            />
          ) : (
            <div className="h-full overflow-auto p-6">
              <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                  <div className="prose prose-lg max-w-none">
                    <div className="whitespace-pre-wrap leading-relaxed text-gray-800">
                      {chapter.modifiedContent || chapter.originalContent}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChapterViewPage;
